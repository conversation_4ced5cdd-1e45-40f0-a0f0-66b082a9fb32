<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="state.dataForm.monitorTime" type="date" value-format="YYYY-MM-DD" placeholder="监测日期"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 300px" v-model="state.dataForm.stationName" :placeholder="$t('zhdc.stationName')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 固定表头并固定站点名称列 -->
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="wystcd" :label="$t('唯一代码')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('zhdc.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="180" prop="stationName" :label="$t('zhdc.stationName')" header-align="center" align="center" fixed="left"></el-table-column>
      <el-table-column width="150" prop="longitude" :label="$t('zhdc.longitude')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="latitude" :label="$t('zhdc.latitude')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="monitorTime" :label="$t('zhdc.monitorTime')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="monitorArea" :label="$t('zhdc.monitorArea')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dissolvedOxygen" :label="$t('zhdc.dissolvedOxygen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ammoniaNitrogen" :label="$t('zhdc.ammoniaNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="biochemicalOxygenDemand" :label="$t('zhdc.biochemicalOxygenDemand')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="transparency" :label="$t('zhdc.transparency')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalPhosphorus" :label="$t('zhdc.totalPhosphorus')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ph" :label="$t('zhdc.ph')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="waterTemperature" :label="$t('zhdc.waterTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="permanganateIndex" :label="$t('zhdc.permanganateIndex')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chlorophyllA" :label="$t('zhdc.chlorophyllA')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chemicalOxygenDemand" :label="$t('zhdc.chemicalOxygenDemand')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalNitrogen" :label="$t('zhdc.totalNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { onMounted, reactive, ref, toRefs, nextTick, onBeforeUnmount } from "vue";
  // 部分组件文件没有 default export，使用命名空间导入以避免构建错误
  import * as AddOrUpdate from "./interface-data-huti-guocai-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import * as UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  import baseService from "@/service/baseService";
  //以下注释不能删除，否则会报错
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  import { SM4Util } from "sm4util/sm4";
  import axios from "axios";
  import { useRoute } from "vue-router"; // 导入 useRoute
  const route = useRoute(); // 使用 useRoute
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/task/interface_data_huti_guocai/page",
    getDataListIsPage: true,
    exportURL: "/task/interface_data_huti_guocai/export",
    deleteURL: "/task/interface_data_huti_guocai",
    uploadURL: "/task/interface_data_huti_guocai/upload",
    ExcelURL: "/task/interface_data_huti_guocai/excel",
    dataForm: {
      id: "",
      stationName: "",
      monitorTime: ""
    }
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  // 表格高度计算，用于固定表头
  const tableHeight = ref<number>(400);
  const calculateTableHeight = () => {
    const offset = 250; // 根据页面上方表单、分页等高度调整
    const h = window.innerHeight - offset;
    tableHeight.value = h > 200 ? h : 200;
  };

  onMounted(() => {
    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateTableHeight);
  });

// 页面加载时检查路由参数并自动查询
  onMounted(() => {
    console.log('页面加载完成，检查路由参数:', route.query);
    if (route.query.stationName) {
      state.dataForm.stationName = route.query.stationName as string;
      console.log('设置stationName:', route.query.stationName);
      // 如果是从monitoringszjhcstation页面跳转过来的，自动执行查询
      if (route.query.from === 'monitoring') {
        console.log('来自monitoring页面，执行自动查询');
        // 使用nextTick确保DOM更新后再执行查询
        nextTick(() => {
          console.log('执行查询');
          state.getDataList();
        });
      } else {
        console.log('不是来自monitoring页面，from参数为:', route.query.from);
      }
    } else {
      console.log('没有stationName参数');
    }
  });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };
</script>

